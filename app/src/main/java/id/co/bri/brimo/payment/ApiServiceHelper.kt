package id.co.bri.brimo.payment

import android.os.Build
import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import id.co.bri.brimo.data.api.ApiService
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.ErangelRequest
import id.co.bri.brimo.payment.core.network.BaseResponse
import id.co.bri.brimo.security.AES
import id.co.bri.brimo.security.MyCryptStatic
import java.util.Date

suspend inline fun <reified T> getDataWithRequest(
    apiService: ApiService,
    brimoPrefSource: BRImoPrefSource,
    gson: <PERSON><PERSON>,
    url: String,
    request: Any
): BaseResponse<T> {
    val request = gson.toJson(request)
    val seqNumber = brimoPrefSource.seqNumber
    val obj: ErangelRequest = generateRequest(
        brimoPrefSource,
        request,
        seqNumber
    )

    val stringResponse = apiService.getGeneralApi(
        setUrlPolos(url),
        obj.request,
        obj.getxDeviceId(),
        obj.getxDevice(),
        obj.getxRandomKey(),
        obj.getxId(),
        obj.xSequenceKey
    )

    val restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNumber)

    return BaseResponse(
        code = restResponse.code,
        description = restResponse.desc,
        data = restResponse.getData(T::class.java)
    )
}

suspend fun hitApi(
    apiService: ApiService,
    brimoPrefSource: BRImoPrefSource,
    gson: Gson,
    url: String,
    request: Any,
    fastMenu: Boolean
): String {
    val requestUpdated = if (request is String && request == "") {
        JsonObject().apply {
            addProperty("username", "")
        }
    } else request
    val requestString = gson.toJson(requestUpdated)
    val request = if (fastMenu) {
        val jsonElement = JsonParser.parseString(requestString)
        val jsonObject = jsonElement.asJsonObject
        jsonObject.addProperty("username", brimoPrefSource.username)
        jsonObject.addProperty("token_key", brimoPrefSource.tokenKey)
        jsonObject.toString()
    } else {
        requestString
    }
    val seqNumber = brimoPrefSource.seqNumber
    val obj: ErangelRequest = generateRequest(
        brimoPrefSource,
        request,
        seqNumber
    )

    val stringResponse = apiService.getGeneralApi(
        setUrlPolos(url),
        obj.request,
        obj.getxDeviceId(),
        obj.getxDevice(),
        obj.getxRandomKey(),
        obj.getxId(),
        obj.xSequenceKey
    )

    val restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNumber)
    val response = gson.toJson(restResponse)

    return response
}

fun generateRequest(
    brimoPrefSource: BRImoPrefSource,
    request: String,
    sequence: String
): ErangelRequest {
    val headerRequest = ErangelRequest()
    var aesRequest: String? = null
    var checksum: String? = null

    val deviceId = brimoPrefSource.getDeviceId()
    val deviceId2 = brimoPrefSource.getDeviceId2()

    try {
        aesRequest = AES.encryptGcm(request, GeneralHelper.getPaddedSeqnum(sequence))

        if (aesRequest != null) {
            checksum = GeneralHelper.md5(aesRequest)

            headerRequest.setxRandomKey(generateRandomXKey(sequence, aesRequest))
            headerRequest.setxDeviceId(deviceId)
            headerRequest.setxDevice(
                TAG_DEVICE_VERSION +
                    TAG_SPACER +
                    Build.MODEL +
                    TAG_SPACER +
                    GeneralHelper.getOSVersion() +
                    TAG_SPACER +
                    GeneralHelper.getLastAppVersion()
            )
            headerRequest.setxId(deviceId2)
            headerRequest.xSequenceKey = sequence
        }
    } catch (error: Throwable) {
        if (!GeneralHelper.isProd()) {
            Log.e(TAG, "AES Encrypt", error)
        }
    }

    headerRequest.request = checksum + aesRequest

    val idleStart = Date().time
    brimoPrefSource.saveTimeIdle(idleStart)

    return headerRequest
}

fun generateRandomXKey(sequence: String, request: String): String {
    val md5Request = GeneralHelper.md5(request)
    val ivRequest = md5Request.substring(Constant.BEGIN_IV, Constant.END_IV)
    val textCipher = AES.encryptGcm(sequence, ivRequest)

    return textCipher
}

fun setUrlPolos(urlLama: String): String {
    var urlPolos = ""
    if (urlLama.isNotEmpty() && urlLama.isNotBlank()) {
        urlPolos = try {
            MyCryptStatic.decryptAsBase64(urlLama)
        } catch (_: Throwable) {
            ""
        }
    }

    return urlPolos
}

private const val TAG_DEVICE_VERSION: String = "2"
private const val TAG_SPACER: String = "{$$}"
private const val TAG: String = "ApiServiceHelper"
