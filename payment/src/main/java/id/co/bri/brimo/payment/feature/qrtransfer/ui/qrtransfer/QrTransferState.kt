package id.co.bri.brimo.payment.feature.qrtransfer.ui.qrtransfer

import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferFormResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse

internal data class QrTransferState(
    val qrTransferForm: QrTransferFormResponse,
    val qrTransferGenerate: UiState<QrTransferGenerateResponse>,
    val accountList: List<AccountResponse> = emptyList()
)

internal sealed class QrTransferEvent {
    object RefreshQrTransferForm : QrTransferEvent()
    data class GenerateQrTransfer(
        val accountNumber: String,
        val amount: String
    ) : QrTransferEvent()
}

internal sealed class QrTransferNavigation {
    object Back : QrTransferNavigation()
    object History : QrTransferNavigation()
}
