package id.co.bri.brimo.payment.feature.qrshow.ui.generate

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrShowGenerateRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asFlowUiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.common.refresh
import id.co.bri.brimo.payment.core.common.uiShare
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmDeleteResponse
import id.co.bri.brimo.payment.feature.qrshow.data.api.QrShowRepository
import id.co.bri.brimo.payment.feature.qrshow.data.model.QrShowModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.launch

internal class QrShowGenerateViewModel(
    private val qrShowRepository: QrShowRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrShowGenerateRoute = savedStateHandle.toRoute<QrShowGenerateRoute>()

    val qrCpmGenerate = runCatching {
        Gson().fromJson(qrShowGenerateRoute.data, QrShowModel::class.java).qrCpmGenerate
    }.getOrNull()

    fun handleEvent(event: QrShowGenerateEvent) {
        when (event) {
            QrShowGenerateEvent.CheckStatus -> {
                refreshQrCpmCheckStatus()
            }

            QrShowGenerateEvent.Delete -> {
                deleteQrCpm()
            }
        }
    }

    val refreshQrCpmCheckStatus = MutableStateFlow(false)
    fun refreshQrCpmCheckStatus() = refreshQrCpmCheckStatus.refresh()

    val qrCpmCheckStatus = refreshQrCpmCheckStatus.flatMapLatest {
        asFlowUiState {
            qrShowRepository.postQrCpmCheckStatus(qrShowGenerateRoute.fastMenu)
        }
    }.uiShare(viewModelScope)

    private val _qrCpmDelete = MutableSharedFlow<UiState<QrCpmDeleteResponse>>()
    val qrCpmDelete = _qrCpmDelete.asSharedFlow()

    private fun deleteQrCpm() {
        viewModelScope.launch {
            _qrCpmDelete.asUiState {
                qrShowRepository.postQrCpmDelete(qrShowGenerateRoute.fastMenu)
            }
        }
    }
}
