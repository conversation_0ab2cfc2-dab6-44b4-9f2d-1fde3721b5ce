package id.co.bri.brimo.payment.feature.qristransfer.ui.nominal

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisTransferNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qristransfer.QrisTransferConfirmationRequest
import id.co.bri.brimo.payment.core.network.response.qristransfer.QrisTransferConfirmationResponse
import id.co.bri.brimo.payment.feature.qristransfer.data.api.QrisTransferRepository
import id.co.bri.brimo.payment.feature.qristransfer.data.model.QrisTransferModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisTransferNominalViewModel(
    private val qrisTransferRepository: QrisTransferRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisTransferNominalRoute = savedStateHandle.toRoute<QrisTransferNominalRoute>()

    val qrisTransferModel = runCatching {
        Gson().fromJson(qrisTransferNominalRoute.data, QrisTransferModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisTransferModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisTransferNominalEvent) {
        when (event) {
            is QrisTransferNominalEvent.Confirmation -> {
                postQrisTransferConfirmation(
                    accountNumber = event.accountNumber,
                    amount = event.amount,
                    note = event.note
                )
            }

            is QrisTransferNominalEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisTransferConfirmation =
        MutableSharedFlow<UiState<QrisTransferConfirmationResponse>>()
    val qrisTransferConfirmation = _qrisTransferConfirmation.asSharedFlow()

    private fun postQrisTransferConfirmation(
        accountNumber: String,
        amount: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisTransferConfirmation.asUiState {
                qrisTransferRepository.postQrisTransferConfirmation(
                    request = QrisTransferConfirmationRequest(
                        accountNumber = accountNumber,
                        amount = amount,
                        note = note,
                        referenceNumber = qrisTransferModel?.qrisScan?.referenceNumber.orEmpty(),
                        saveAs = ""
                    ),
                    fastMenu = qrisTransferNominalRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisTransferRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
