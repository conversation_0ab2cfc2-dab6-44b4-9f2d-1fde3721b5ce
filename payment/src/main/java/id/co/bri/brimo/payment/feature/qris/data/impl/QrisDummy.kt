package id.co.bri.brimo.payment.feature.qris.data.impl

internal val scanQrisDummy = """
    {
      "code": "00",
      "description": "Suks<PERSON>",
      "data": {
        "account_list": [
          {
            "account": "***************",
            "account_string": "0230 0113 7115 507",
            "name": "ADIXXXXXXXXXXXXXXLTI",
            "currency": "Rp",
            "card_number": "5221XXXXXXXX7777",
            "card_number_string": "5221 XXXX XXXX 7777",
            "product_type": "BritAma",
            "account_type": "SA",
            "sc_code": "TA",
            "default": 1,
            "alias": "",
            "minimum_balance": 50000,
            "limit": -1,
            "limit_string": "",
            "image_name": "BritAma.png",
            "image_path": "http://*************:4010/brimo-asset/account_logo/BritAma.png"
          },
          {
            "account": "***************",
            "account_string": "0206 0107 7765 506",
            "name": "Minus",
            "currency": "Rp",
            "card_number": "5221XXXXXXXX9364",
            "card_number_string": "5221 XXXX XXXX 9364",
            "product_type": "Simpedes",
            "account_type": "SA",
            "sc_code": "TA",
            "default": 0,
            "alias": "",
            "minimum_balance": 50000,
            "limit": -1,
            "limit_string": "",
            "image_name": "BritAma.png",
            "image_path": "http://*************:4010/brimo-asset/account_logo/BritAma.png"
          },
          {
            "account": "***************",
            "account_string": "0230 0113 7104 506",
            "name": "ADIXXXXXXXXXXXXXXLTI",
            "currency": "Rp",
            "card_number": "5221XXXXXXXX7777",
            "card_number_string": "5221 XXXX XXXX 7777",
            "product_type": "BritAma",
            "account_type": "SA",
            "sc_code": "TA",
            "default": 0,
            "alias": "",
            "minimum_balance": 50000,
            "limit": -1,
            "limit_string": "",
            "image_name": "BritAma.png",
            "image_path": "http://*************:4010/brimo-asset/account_logo/BritAma.png"
          }
        ],
        "input_data_from": {
          "country_name": "Singapore",
          "country_code": "SG",
          "currency_code": "SGD",
          "icon_name": "Singapore.png",
          "icon_path": "https://s3.brimo.bri.co.id/brimo-asset/qris_cb/Singapore.png",
          "input_name": "Singapore Dollar",
          "input_value": 0
        },
        "information": {
          "name": "Informasi",
          "value": "<!DOCTYPE html><html><head><style>.paragraphs{text-align:justify;font-family:Arial,sans-serif;font-size: 12px;}.paragraphs li{margin-bottom:2px}</style></head><body><div class=\"paragraphs\"><ol><li>Jam operasional & jam transaksi QR Code pada BRImo menggunakan Waktu Indonesia Barat (WIB)</li><li>Limit Pembayaran & Pembelian per hari: Reguler (Rp 10.000.000), Premium (Rp 20.000.000)</li><li>Limit Pembayaran & Pembelian per 1x transaksi: Rp 10.000.000</li></ol></div></body></html>",
          "style": ""
        },
        "open_payment": true,
        "is_billing": false,
        "minimum_payment": false,
        "amount_editable": true,
        "row_data_show": 0,
        "saved": "-",
        "minimum_amount": 0,
        "minimum_amount_string": "Rp0",
        "minimum_transaction": 1,
        "minimum_transaction_string": "Rp1",
        "type_qr": "qris_cb",
        "merchant_name": "NETS LV TESTING",
        "merchant_country": "Singapore",
        "reference_number": "323549894635"
      }
    }
""".trimIndent()
