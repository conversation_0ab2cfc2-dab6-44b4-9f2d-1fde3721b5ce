package id.co.bri.brimo.payment.feature.qriscrossborder.ui.nominal

import id.co.bri.brimo.payment.app.QrisCrossNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qriscrossborder.QrisCrossConfirmationResponse
import id.co.bri.brimo.payment.feature.qriscrossborder.data.model.QrisCrossModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class QrisCrossNominalState(
    val qrisCrossNominalRoute: QrisCrossNominalRoute,
    val qrisCrossModel: QrisCrossModel,
    val accountList: List<AccountResponse> = emptyList(),
    val qrisCrossConfirmation: SharedFlow<UiState<QrisCrossConfirmationResponse>> = MutableSharedFlow()
)

internal sealed class QrisCrossNominalEvent {
    data class Confirmation(
        val accountNumber: String,
        val amount: String,
        val note: String
    ) : QrisCrossNominalEvent()

    data class RefreshSaldo(
        val account: String
    ) : QrisCrossNominalEvent()
}

internal sealed class QrisCrossNominalNavigation {
    object Back : QrisCrossNominalNavigation()
    data class Confirmation(
        val qrisCrossData: String,
        val fastMenu: Boolean
    ) : QrisCrossNominalNavigation()
}
