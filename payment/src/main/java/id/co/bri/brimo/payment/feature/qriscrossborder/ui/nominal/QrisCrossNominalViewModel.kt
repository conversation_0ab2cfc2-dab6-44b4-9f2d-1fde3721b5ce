package id.co.bri.brimo.payment.feature.qriscrossborder.ui.nominal

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisCrossNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qriscrossborder.QrisCrossConfirmationRequest
import id.co.bri.brimo.payment.core.network.response.qriscrossborder.QrisCrossConfirmationResponse
import id.co.bri.brimo.payment.feature.qriscrossborder.data.api.QrisCrossBorderRepository
import id.co.bri.brimo.payment.feature.qriscrossborder.data.model.QrisCrossModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisCrossNominalViewModel(
    private val qrisCrossRepository: QrisCrossBorderRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisCrossNominalRoute = savedStateHandle.toRoute<QrisCrossNominalRoute>()

    val qrisCrossModel = runCatching {
        Gson().fromJson(qrisCrossNominalRoute.data, QrisCrossModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisCrossModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisCrossNominalEvent) {
        when (event) {
            is QrisCrossNominalEvent.Confirmation -> {
                postQrisCrossConfirmation(
                    accountNumber = event.accountNumber,
                    amount = event.amount,
                    note = event.note
                )
            }

            is QrisCrossNominalEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisCrossConfirmation =
        MutableSharedFlow<UiState<QrisCrossConfirmationResponse>>()
    val qrisCrossConfirmation = _qrisCrossConfirmation.asSharedFlow()

    private fun postQrisCrossConfirmation(
        accountNumber: String,
        amount: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisCrossConfirmation.asUiState {
                qrisCrossRepository.postQrisCrossConfirmation(
                    request = QrisCrossConfirmationRequest(
                        accountNumber = accountNumber,
                        amount = amount,
                        note = note,
                        referenceNumber = qrisCrossModel?.qrisScan?.referenceNumber.orEmpty(),
                        saveAs = ""
                    ),
                    fastMenu = qrisCrossNominalRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisCrossRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}