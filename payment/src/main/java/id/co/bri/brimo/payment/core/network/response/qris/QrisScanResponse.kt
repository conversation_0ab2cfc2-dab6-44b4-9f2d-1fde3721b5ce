package id.co.bri.brimo.payment.core.network.response.qris

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse

internal data class QrisScanResponse(
    @SerializedName("account_list") val accountList: List<AccountResponse>?,
    @SerializedName("billing_detail") val billingDetail: List<BillingResponse>?,
    @SerializedName("billing_detail_open") val billingDetailOpen: List<BillingResponse>?,
    @SerializedName("billing_amount") val billingAmount: List<DataViewResponse>?,
    @SerializedName("billing_amount_detail") val billingAmountDetail: List<DataViewResponse>?,
    @SerializedName("input_data_from") val inputDataFrom: InputDataFrom?,
    @SerializedName("information") val information: DataViewResponse?,
    @SerializedName("open_payment") val openPayment: Boolean?,
    @SerializedName("is_billing") val isBilling: Boolean?,
    @SerializedName("minimum_payment") val minimumPayment: Boolean?,
    @SerializedName("amount_editable") val amountEditable: Boolean?,
    @SerializedName("row_data_show") val rowDataShow: Int?,
    @SerializedName("saved") val saved: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("amount_string") val amountString: String?,
    @SerializedName("minimum_amount") val minimumAmount: String?,
    @SerializedName("minimum_amount_string") val minimumAmountString: String?,
    @SerializedName("admin_fee") val adminFee: String?,
    @SerializedName("admin_fee_string") val adminFeeString: String?,
    @SerializedName("pay_amount") val payAmount: String?,
    @SerializedName("pay_amount_string") val payAmountString: String?,
    @SerializedName("minimum_transaction") val minimumTransaction: String?,
    @SerializedName("minimum_transaction_string") val minimumTransactionString: String?,
    @SerializedName("tip_option") val tipOption: List<TipOption>?,
    @SerializedName("tip_editable") val tipEditable: Boolean?,
    @SerializedName("tip_type") val tipType: String?,
    @SerializedName("tip_amount") val tipAmount: String?,
    @SerializedName("tip_amount_string") val tipAmountString: String?,
    @SerializedName("type_qr") val typeQr: String?,
    @SerializedName("merchant_name") val merchantName: String?,
    @SerializedName("merchant_country") val merchantCountry: String?,
    @SerializedName("reference_number") val referenceNumber: String?
)

internal data class InputDataFrom(
    @SerializedName("country_name") val countryName: String?,
    @SerializedName("country_code") val countryCode: String?,
    @SerializedName("currency_code") val currencyCode: String?,
    @SerializedName("icon_name") val iconName: String?,
    @SerializedName("icon_path") val iconPath: String?,
    @SerializedName("input_name") val inputName: String?,
    @SerializedName("input_value") val inputValue: Int?
)

internal data class TipOption(
    @SerializedName("text") val text: String?,
    @SerializedName("value") val value: Int?
)
