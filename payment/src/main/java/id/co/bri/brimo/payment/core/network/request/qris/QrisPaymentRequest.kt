package id.co.bri.brimo.payment.core.network.request.qris

import com.google.gson.annotations.SerializedName

internal data class QrisPaymentRequest(
    @SerializedName("note") val note: String?,
    @SerializedName("pfm_category") val pfmCategory: Int?,
    @SerializedName("pin") val pin: String?,
    @SerializedName("reference_number") val referenceNumber: String?,
    @SerializedName("account_number") val accountNumber: String? = null
)
