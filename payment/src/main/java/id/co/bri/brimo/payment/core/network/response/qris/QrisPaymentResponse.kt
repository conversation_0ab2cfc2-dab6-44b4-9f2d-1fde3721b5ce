package id.co.bri.brimo.payment.core.network.response.qris

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.base.DataViewResponse

internal data class QrisPaymentResponse(
    @SerializedName("amount_data_view") val amountDataView: List<DataViewResponse>?,
    @SerializedName("billing_detail") val billingDetail: BillingResponse?,
    @SerializedName("close_button_string") val closeButtonString: String?,
    @SerializedName("data_view_transaction") val dataViewTransaction: List<DataViewResponse>?,
    @SerializedName("date_transaction") val dateTransaction: String?,
    @SerializedName("footer") val footer: String?,
    @SerializedName("footer_html") val footerHtml: String?,
    @SerializedName("header_data_view") val headerDataView: List<DataViewResponse>?,
    @SerializedName("help_flag") val helpFlag: Boolean?,
    @SerializedName("immediately_flag") val immediatelyFlag: Boolean?,
    @SerializedName("on_process") val onProcess: Boolean?,
    @SerializedName("reference_number") val referenceNumber: String?,
    @SerializedName("row_data_show") val rowDataShow: Int?,
    @SerializedName("share") val share: Boolean?,
    @SerializedName("share_button_string") val shareButtonString: String?,
    @SerializedName("source_account_data_view") val sourceAccountDataView: BillingResponse?,
    @SerializedName("source_account_list_type") val sourceAccountListType: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("title_image") val titleImage: String?,
    @SerializedName("total_data_view") val totalDataView: List<DataViewResponse>?,
    @SerializedName("voucher_data_view") val voucherDataView: List<DataViewResponse>?,
    @SerializedName("kurs_data_view") val kursDataView: List<DataViewResponse>?
)
