package id.co.bri.brimo.payment.feature.qrshow.ui.generate

import id.co.bri.brimo.payment.app.QrShowGenerateRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmDeleteResponse
import id.co.bri.brimo.payment.core.network.response.qrshow.QrCpmGenerateResponse
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

internal data class QrShowGenerateState(
    val qrShowGenerateRoute: QrShowGenerateRoute,
    val qrCpmGenerate: QrCpmGenerateResponse,
    val qrCpmCheckStatus: SharedFlow<UiState<QrCpmCheckStatusResponse>> = MutableSharedFlow(),
    val qrCpmDelete: SharedFlow<UiState<QrCpmDeleteResponse>> = MutableSharedFlow()
)

internal sealed class QrShowGenerateEvent {
    object CheckStatus : QrShowGenerateEvent()
    object Delete : QrShowGenerateEvent()
}

internal sealed class QrShowGenerateNavigation {
    object Back : QrShowGenerateNavigation()
    data class Payment(val data: String) : QrShowGenerateNavigation()
    data class Delete(val fastMenu: Boolean, val inclusive: Boolean = false) : QrShowGenerateNavigation()
}
