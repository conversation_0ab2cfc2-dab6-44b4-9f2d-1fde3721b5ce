package id.co.bri.brimo.payment.feature.qrisscan.data.model

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.core.network.response.qrisscan.QrisScanConfirmationResponse

internal data class QrisScanModel(
    @SerializedName("qrisScan") val qrisScan: QrisScanResponse? = null,
    @SerializedName("qrisScanConfirmation") val qrisScanConfirmation: QrisScanConfirmationResponse? = null,
    @SerializedName("accountNumber") val accountNumber: String? = null
)
