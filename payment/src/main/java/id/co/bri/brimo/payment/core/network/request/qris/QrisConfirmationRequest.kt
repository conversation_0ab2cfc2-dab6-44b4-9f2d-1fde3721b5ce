package id.co.bri.brimo.payment.core.network.request.qris

import com.google.gson.annotations.SerializedName

internal data class QrisConfirmationRequest(
    @SerializedName("account_number") val accountNumber: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("note") val note: String?,
    @SerializedName("reference_number") val referenceNumber: String?,
    @SerializedName("save_as") val saveAs: String?,
    @SerializedName("input_tip_amount") val inputTipAmount: String? = null
)
