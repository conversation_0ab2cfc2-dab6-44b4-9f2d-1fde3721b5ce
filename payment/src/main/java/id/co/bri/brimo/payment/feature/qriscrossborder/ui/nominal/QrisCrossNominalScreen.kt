package id.co.bri.brimo.payment.feature.qriscrossborder.ui.nominal

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrisCrossNominalRoute
import id.co.bri.brimo.payment.core.common.launchAndCollectIn
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onLoading
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.CurrencyCentFieldWithFlag
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.component.TextFieldCustom
import id.co.bri.brimo.payment.core.design.component.textFieldColors
import id.co.bri.brimo.payment.core.design.helper.InitialsAvatar
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_F5F7FB
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.base.BillingResponse
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.feature.qriscrossborder.data.model.CurrencyRate
import id.co.bri.brimo.payment.feature.qriscrossborder.data.model.QrisCrossModel
import id.co.bri.brimo.payment.feature.qriscrossborder.ui.base.QrisCrossAccountBottomSheet
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale

@Composable
internal fun QrisCrossNominalScreenOk(
    navigation: (QrisCrossNominalNavigation) -> Unit = {},
    qrisCrossNominalViewModel: QrisCrossNominalViewModel = koinViewModel()
) {
    val accountList by qrisCrossNominalViewModel.accountList.collectAsStateWithLifecycle()

    if (qrisCrossNominalViewModel.qrisCrossModel != null) {
        QrisCrossNominalContent(
            state = QrisCrossNominalState(
                qrisCrossNominalRoute = qrisCrossNominalViewModel.qrisCrossNominalRoute,
                qrisCrossModel = qrisCrossNominalViewModel.qrisCrossModel,
                accountList = accountList.orEmpty(),
                qrisCrossConfirmation = qrisCrossNominalViewModel.qrisCrossConfirmation
            ),
            event = qrisCrossNominalViewModel::handleEvent,
            navigation = navigation
        )
    }
}

@Composable
private fun QrisCrossNominalContent(
    state: QrisCrossNominalState,
    event: (QrisCrossNominalEvent) -> Unit = {},
    navigation: (QrisCrossNominalNavigation) -> Unit = {}
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // Global
    val dummyCurrencyRates = listOf(
        CurrencyRate(
            currency_pair = "SGD-IDR",
            currency1 = "SGD",
            currency2 = "IDR",
            bid = 12200.015,
            ask = 12327.115
        ),
        CurrencyRate(
            currency_pair = "MYR-IDR",
            currency1 = "MYR",
            currency2 = "IDR",
            bid = 3654.2601,
            ask = 3699.0751
        )
    )

    val locale = try {
        Locale("id", "ID")
    } catch (e: Exception) {
        Locale.US
    }

    var foreignCentValue by remember { mutableStateOf(0L) }

    val foreignNumberFormat = remember(locale) {
        DecimalFormat("#,##0.00", DecimalFormatSymbols(locale)).apply {
            isParseBigDecimal = true
            roundingMode = RoundingMode.HALF_UP
        }
    }

    var foreignCurrencyDisplayText by remember {
        mutableStateOf(foreignNumberFormat.format(foreignCentValue / 100.0))
    }

    val currencyCode1 = state.qrisCrossModel?.qrisScan?.inputDataFrom?.currencyCode

    val exchangeRateEntry = dummyCurrencyRates.firstOrNull {
        it.currency1 == currencyCode1 && it.currency2 == "IDR"
    }
    val exchangeRate1 = exchangeRateEntry?.ask
    // Sebelumnya:
    /*val idrAmount = remember(foreignCentValue, exchangeRate1) {
        (foreignCentValue / 100.0) * exchangeRate1
    }*/
    val idrAmount: BigDecimal? = remember(foreignCentValue, exchangeRate1) {
        exchangeRate1?.let {
            val centValue = BigDecimal(foreignCentValue).divide(BigDecimal(100))
            val rate = BigDecimal(it)
            centValue.multiply(rate)
        }
    }

    val idrFormatted = remember(idrAmount) {
        idrAmount?.let { formatCurrencyWithoutSymbol(it, language = "id", country = "ID") }
    }

    val amount = BigDecimal(foreignCentValue).divide(BigDecimal(100)).toPlainString()

    var noteField by rememberSaveable { mutableStateOf("") }
    val counterNote by remember {
        derivedStateOf {
            "${noteField.length}/30"
        }
    }
    val enableButton by remember {
        derivedStateOf {
            foreignCentValue >= (state.qrisCrossModel.qrisScan?.minimumTransaction?.toDouble()
                ?.toLong() ?: 0L)
        }
    }

    var selectedAccount: AccountResponse? by remember {
        mutableStateOf(state.accountList.firstOrNull())
    }

    val foreignAmount =
        BigDecimal(foreignCentValue).divide(BigDecimal(100)).multiply(exchangeRate1?.let {
            BigDecimal(
                it
            )
        })
    val notEnough = (selectedAccount?.balance?.toBigDecimal() ?: BigDecimal.ZERO) < foreignAmount

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(
                QrisCrossNominalEvent.Confirmation(
                    accountNumber = selectedAccount?.account.orEmpty(),
                    amount = amount,
                    note = noteField
                )
            )
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisCrossAccountBottomSheet(
                nominal = amount,
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                    }
                },
                onRefresh = { item ->
                    event(QrisCrossNominalEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                }
            )
        }
    }

    // qrisCross Confirmation
    LaunchedEffect(Unit) {
        state.qrisCrossConfirmation.launchAndCollectIn(lifecycleOwner) { event ->
            event
                .onLoading {
                    progressDialog = true
                }
                .onSuccess { data ->
                    progressDialog = false
                    val qrisCrossModel = QrisCrossModel(
                        qrisCrossConfirmation = data,
                        accountNumber = selectedAccount?.account
                    )
                    val qrisCrossData = qrisCrossModel.serialize().orEmpty()
                    if (qrisCrossData.isEmpty()) {
                        errorBottomSheet = true
                    } else {
                        //val formatted = formatCurrency(nominalConvert.toDouble(), "id", "ID")
                        navigation(
                            QrisCrossNominalNavigation.Confirmation(
                                qrisCrossData = qrisCrossData,
                                fastMenu = state.qrisCrossNominalRoute.fastMenu,
                            )
                        )
                    }
                }
                .onError { e ->
                    progressDialog = false
                    if (e is MessageException) {
                        scope.launch {
                            snackbarType = SnackbarType.ERROR
                            snackbarHostState.showSnackbar(e.description)
                        }
                    } else {
                        error = e
                        errorBottomSheet = true
                    }
                }
        }
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures {
                    focusManager.clearFocus()
                }
            },
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, top = 16.dp, end = 16.dp)
                        .clickable {
                            accountBottomSheet = true
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = selectedAccount?.imagePath.orEmpty(),
                        contentDescription = null,
                        modifier = Modifier
                            .width(58.dp)
                            .height(36.dp),
                        placeholder = painterResource(id = R.drawable.thumbnail),
                        error = painterResource(id = R.drawable.thumbnail),
                        contentScale = ContentScale.Inside
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    var hide by rememberSaveable { mutableStateOf(false) }
                    val icon = if (hide) {
                        R.drawable.icon_hide_eye
                    } else {
                        R.drawable.icon_unhide_eye
                    }
                    val balance = if (hide) {
                        "••••••"
                    } else {
                        selectedAccount?.balanceString.orEmpty()
                    }
                    val balanceError = selectedAccount?.balanceString == null
                    val color = if (notEnough || balanceError) Color_E84040 else Color.Black

                    Column(modifier = Modifier.weight(1f)) {
                        Row(
                            modifier = Modifier.clickable {
                                hide = !hide
                            },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = selectedAccount?.accountString.orEmpty(),
                                style = MaterialTheme.typography.labelSmall
                            )

                            if (!balanceError) {
                                Spacer(modifier = Modifier.width(4.dp))

                                Image(
                                    painter = painterResource(icon),
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp),
                                    contentScale = ContentScale.Fit
                                )
                            }
                        }

                        Row(verticalAlignment = Alignment.CenterVertically) {
                            if (!balanceError) {
                                Text(
                                    text = selectedAccount?.currency.orEmpty() + balance,
                                    color = color,
                                    fontWeight = FontWeight.SemiBold,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.width(8.dp))
                            }

                            if (balanceError) {
                                Text(
                                    text = "Gagal memuat saldo",
                                    color = Color_E84040,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp)
                    )
                }

                PrimaryButton(
                    label = "Lanjutkan",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    enabled = enableButton && !notEnough
                ) {
                    focusManager.clearFocus()
                    event(
                        QrisCrossNominalEvent.Confirmation(
                            accountNumber = selectedAccount?.account.orEmpty(),
                            //amount = nominalField.toString(),
                            amount = amount,
                            note = noteField
                        )
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrisCrossNominalNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "Scan QRIS",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = "Penerima",
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.SemiBold,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color_F5F7FB, RoundedCornerShape(16.dp))
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val iconPath =
                            state.qrisCrossModel.qrisScan?.billingDetailOpen?.firstOrNull()?.iconPath.orEmpty()
                        val merchantName = state.qrisCrossModel.qrisScan?.merchantName.orEmpty()

                        val painter = rememberAsyncImagePainter(
                            model = iconPath,
                            placeholder = painterResource(R.drawable.thumbnail),
                            error = painterResource(R.drawable.thumbnail)
                        )

                        if (painter.state is AsyncImagePainter.State.Error || iconPath.isBlank()) {
                            InitialsAvatar(name = merchantName)
                        } else {
                            Image(
                                painter = painter,
                                contentDescription = null,
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape),
                                contentScale = ContentScale.Crop
                            )
                        }

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = state.qrisCrossModel.qrisScan?.merchantName.orEmpty(),
                                modifier = Modifier.fillMaxWidth(),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(2.dp))

                            val subtitle =
                                state.qrisCrossModel.qrisScan?.billingDetailOpen?.firstOrNull()?.subtitle.orEmpty()
                            val description =
                                state.qrisCrossModel.qrisScan?.merchantCountry.orEmpty()
                            Text(
                                text = description,
                                modifier = Modifier.fillMaxWidth(),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    CurrencyCentFieldWithFlag(
                        value = foreignCurrencyDisplayText,
                        value2 = idrFormatted,
                        maxTransferableAmount = selectedAccount?.balance?.toDoubleOrNull()
                            ?.toLong(),
                        onValueChange = { newInput ->
                            val digitsOnly = newInput.filter { it.isDigit() }
                            foreignCentValue = digitsOnly.take(15).toLongOrNull() ?: 0L
                            foreignCurrencyDisplayText =
                                foreignNumberFormat.format(foreignCentValue / 100.0)
                        },
                        placeholder = "Nominal Pembayaran",
                        minValue = state.qrisCrossModel.qrisScan?.minimumTransaction?.toDoubleOrNull()
                            ?.toLong(),
                        flagName = state.qrisCrossModel.qrisScan?.inputDataFrom?.currencyCode.orEmpty(),
                        flagImage = state.qrisCrossModel.qrisScan?.inputDataFrom?.iconPath.orEmpty(),
                        enabled = true,
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    idrFormatted?.let {
                        CurrencyCentFieldWithFlag(
                            value = it,
                            onValueChange = {},
                            placeholder = "Konversi",
                            enabled = false,
                            flagName = "IDR",
                            flagImage = "id",
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    TextFieldCustom(
                        value = noteField,
                        onValueChange = {
                            if (it.length <= 30) {
                                noteField = it
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(64.dp),
                        textStyle = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        label = {
                            Text(text = "Catatan (Opsional)")
                        },
                        trailingIcon = if (noteField.isNotEmpty()) {
                            {
                                Image(
                                    painter = painterResource(R.drawable.icon_close),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(16.dp)
                                        .clickable {
                                            noteField = ""
                                        },
                                    contentScale = ContentScale.Fit
                                )
                            }
                        } else {
                            null
                        },
                        singleLine = true,
                        shape = RoundedCornerShape(16.dp),
                        colors = textFieldColors(),
                        contentPadding = TextFieldDefaults.contentPaddingWithLabel(top = 12.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = counterNote,
                        modifier = Modifier.align(Alignment.End),
                        color = Color_7B90A6,
                        style = MaterialTheme.typography.bodySmall
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.systemBars))
                }
            }
        }
    }
}

fun formatCurrencyWithoutSymbol(
    amount: BigDecimal,
    language: String,
    country: String
): String {
    val locale = Locale(language, country)
    val symbols = DecimalFormatSymbols(locale)
    val formatter = DecimalFormat("#,##0.00", symbols)
    formatter.roundingMode = RoundingMode.CEILING
    return formatter.format(amount)
}


@Preview
@Composable
private fun PreviewQrisCrossNominal() {
    MainTheme {
        QrisCrossNominalContent(
            state = QrisCrossNominalState(
                qrisCrossNominalRoute = QrisCrossNominalRoute(
                    data = "",
                    fastMenu = false
                ),
                qrisCrossModel = QrisCrossModel(
                    qrisScan = QrisScanResponse(
                        accountList = listOf(
                            AccountResponse(
                                account = "***************",
                                accountString = "0230 0113 7115 507",
                                name = "ADIXXXXXXXXXXXXXXLTI",
                                currency = "Rp",
                                cardNumber = "5221XXXXXXXX7777",
                                cardNumberString = "5221 XXXX XXXX 7777",
                                productType = "BritAma",
                                accountType = "SA",
                                scCode = "TA",
                                default = 0,
                                alias = "",
                                minimumBalance = "50000",
                                limit = "-1",
                                limitString = "",
                                imageName = "",
                                imagePath = "",
                                onHold = false,
                                balance = "9085000",
                                balanceString = "9.085.000,00"
                            )
                        ),
                        billingDetail = listOf(),
                        billingDetailOpen = listOf(
                            BillingResponse(
                                listType = "",
                                iconName = "",
                                iconPath = "",
                                title = "TESTING123",
                                subtitle = "Dummy",
                                description = "8811812345001"
                            )
                        ),
                        billingAmount = listOf(),
                        billingAmountDetail = listOf(),
                        openPayment = false,
                        isBilling = false,
                        minimumPayment = false,
                        rowDataShow = 1,
                        saved = "",
                        amount = "",
                        amountString = "",
                        minimumAmount = "",
                        minimumAmountString = "",
                        adminFee = "",
                        adminFeeString = "",
                        payAmount = "",
                        payAmountString = "",
                        minimumTransaction = "0",
                        minimumTransactionString = "",
                        referenceNumber = "",
                        inputDataFrom = null,
                        information = null,
                        amountEditable = false,
                        tipOption = listOf(),
                        tipEditable = false,
                        tipType = "",
                        tipAmount = "",
                        tipAmountString = "",
                        typeQr = "",
                        merchantName = "NETS LV TESTING",
                        merchantCountry = "SINGAPORE"
                    )
                )
            )
        )
    }
}
