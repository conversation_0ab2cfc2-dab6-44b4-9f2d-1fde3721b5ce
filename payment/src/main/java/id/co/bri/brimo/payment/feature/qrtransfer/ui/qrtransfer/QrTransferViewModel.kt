package id.co.bri.brimo.payment.feature.qrtransfer.ui.qrtransfer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.common.getDateTimeSecond
import id.co.bri.brimo.payment.core.common.refresh
import id.co.bri.brimo.payment.core.common.uiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtransfer.QrTransferFormRequest
import id.co.bri.brimo.payment.core.network.request.qrtransfer.QrTransferGenerateRequest
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.core.network.response.qrtransfer.QrTransferGenerateResponse
import id.co.bri.brimo.payment.feature.qrtransfer.data.api.QrTransferRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrTransferViewModel(
    private val qrTransferRepository: QrTransferRepository
) : ViewModel() {

    private val _accountList = MutableStateFlow<List<AccountResponse>?>(null)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrTransferEvent) {
        when (event) {
            QrTransferEvent.RefreshQrTransferForm -> {
                refreshQrTransferForm()
            }

            is QrTransferEvent.GenerateQrTransfer -> {
                generateQrTransfer(event.accountNumber, event.amount)
            }
        }
    }

    private val refreshQrTransferForm = MutableStateFlow(false)
    private fun refreshQrTransferForm() = refreshQrTransferForm.refresh()

    val qrTransferForm = refreshQrTransferForm.flatMapLatest {
        callbackFlow {
            send(UiState.Loading)
            try {
                val qrTransferForm = qrTransferRepository.postQrTransferForm(
                    QrTransferFormRequest(
                        deletedMpan = "",
                        newQr = true
                    )
                )

                val accountList = qrTransferForm.accountList?.map { account ->
                    async {
                        try {
                            val saldoNormal = qrTransferRepository.postSaldoNormal(
                                request = SaldoNormalRequest(
                                    account = account.account.orEmpty()
                                )
                            )
                            account.copy(
                                onHold = saldoNormal.onHold,
                                balance = saldoNormal.balance,
                                balanceString = saldoNormal.balanceString
                            )
                        } catch (_: Throwable) {
                            account
                        }
                    }
                }?.awaitAll()

                _accountList.update { accountList }

                val qrTransferFormUpdated = qrTransferForm.copy(accountList = accountList)

                generateQrTransfer(accountList?.firstOrNull()?.account.orEmpty(), "")

                send(UiState.Success(qrTransferFormUpdated))
            } catch (error: Throwable) {
                send(UiState.Error(error))
            } finally {
                close()
            }
        }
    }.uiState(viewModelScope)

    private val _qrTransferGenerate =
        MutableStateFlow<UiState<QrTransferGenerateResponse>>(UiState.Init)
    val qrTransferGenerate = _qrTransferGenerate.asStateFlow()

    private fun generateQrTransfer(accountNumber: String, amount: String) {
        viewModelScope.launch {
            _qrTransferGenerate.asUiState {
                val qrTransferGenerate = qrTransferRepository.postQrTransferGenerate(
                    QrTransferGenerateRequest(
                        accountNumber = accountNumber,
                        amount = amount.ifEmpty { "0" },
                        deletedMpan = ""
                    )
                )
                val qrTransferGenerateUpdated = qrTransferGenerate.copy(
                    expired = getDateTimeSecond(qrTransferGenerate.expired?.toIntOrNull() ?: 0)
                )
                qrTransferGenerateUpdated
            }
        }
    }
}
