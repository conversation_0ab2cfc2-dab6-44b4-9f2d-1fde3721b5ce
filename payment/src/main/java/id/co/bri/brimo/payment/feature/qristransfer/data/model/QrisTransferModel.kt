package id.co.bri.brimo.payment.feature.qristransfer.data.model

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.core.network.response.qristransfer.QrisTransferConfirmationResponse

internal data class QrisTransferModel(
    @SerializedName("qrisScan") val qrisScan: QrisScanResponse? = null,
    @SerializedName("qrisTransferConfirmation") val qrisTransferConfirmation: QrisTransferConfirmationResponse? = null,
    @SerializedName("accountNumber") val accountNumber: String? = null
)
