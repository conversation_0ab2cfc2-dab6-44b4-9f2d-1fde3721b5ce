package id.co.bri.brimo.payment.feature.qriscrossborder.data.model

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.payment.core.network.response.qris.QrisScanResponse
import id.co.bri.brimo.payment.core.network.response.qriscrossborder.QrisCrossConfirmationResponse
internal data class QrisCrossModel(
    @SerializedName("qrisScan") val qrisScan: QrisScanResponse? = null,
    @SerializedName("qrisCrossConfirmation") val qrisCrossConfirmation: QrisCrossConfirmationResponse? = null,
    @SerializedName("accountNumber") val accountNumber: String? = null
)
