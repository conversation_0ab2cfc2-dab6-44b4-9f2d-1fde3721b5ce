package id.co.bri.brimo.payment.feature.qris.ui.receipt

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.ReceiptQrisRoute
import id.co.bri.brimo.payment.core.network.response.qris.QrisPaymentResponse

internal class ReceiptQrisViewModel(
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val receiptRoute = savedStateHandle.toRoute<ReceiptQrisRoute>()

    val receiptData = runCatching {
        Gson().fromJson(receiptRoute.data, QrisPaymentResponse::class.java)
    }.getOrNull()
}
