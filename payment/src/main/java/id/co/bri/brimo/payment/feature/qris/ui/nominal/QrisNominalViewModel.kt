package id.co.bri.brimo.payment.feature.qris.ui.nominal

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.google.gson.Gson
import id.co.bri.brimo.payment.app.QrisNominalRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.asUiState
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qris.QrisConfirmationRequest
import id.co.bri.brimo.payment.core.network.response.qris.QrisConfirmationResponse
import id.co.bri.brimo.payment.feature.qris.data.api.QrisRepository
import id.co.bri.brimo.payment.feature.qris.data.model.QrisModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class QrisNominalViewModel(
    private val qrisRepository: QrisRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    val qrisNominalRoute = savedStateHandle.toRoute<QrisNominalRoute>()

    val qrisModel = runCatching {
        Gson().fromJson(qrisNominalRoute.data, QrisModel::class.java)
    }.getOrNull()

    private val _accountList = MutableStateFlow(qrisModel?.qrisScan?.accountList)
    val accountList = _accountList.asStateFlow()

    fun handleEvent(event: QrisNominalEvent) {
        when (event) {
            is QrisNominalEvent.Confirmation -> {
                postQrisConfirmation(
                    accountNumber = event.accountNumber,
                    amount = event.amount,
                    tipAmount = event.tipAmount,
                    note = event.note
                )
            }

            is QrisNominalEvent.RefreshSaldo -> {
                refreshSaldo(account = event.account)
            }
        }
    }

    private val _qrisConfirmation =
        MutableSharedFlow<UiState<QrisConfirmationResponse>>()
    val qrisConfirmation = _qrisConfirmation.asSharedFlow()

    private fun postQrisConfirmation(
        accountNumber: String,
        amount: String,
        tipAmount: String,
        note: String
    ) {
        viewModelScope.launch {
            _qrisConfirmation.asUiState {
                qrisRepository.postQrisConfirmation(
                    type = qrisModel?.typeQr.orEmpty(),
                    request = QrisConfirmationRequest(
                        accountNumber = accountNumber,
                        amount = amount,
                        note = note,
                        referenceNumber = qrisModel?.qrisScan?.referenceNumber.orEmpty(),
                        saveAs = "",
                        inputTipAmount = tipAmount.takeIf { it.isNotEmpty() }
                    ),
                    fastMenu = qrisNominalRoute.fastMenu
                )
            }
        }
    }

    private fun refreshSaldo(account: String) {
        viewModelScope.launch {
            updateAccountList(account, true)
            try {
                val saldoNormal = qrisRepository.postSaldoNormal(
                    request = SaldoNormalRequest(
                        account = account
                    )
                )
                val accountListUpdated = accountList.value?.map { item ->
                    if (item.account == account) {
                        item.copy(
                            onHold = saldoNormal.onHold,
                            balance = saldoNormal.balance,
                            balanceString = saldoNormal.balanceString,
                            loading = false
                        )
                    } else {
                        item
                    }
                }
                _accountList.update { accountListUpdated }
            } catch (_: Throwable) {
                updateAccountList(account, false)
            }
        }
    }

    private fun updateAccountList(account: String, loading: Boolean) {
        val accountListUpdated = accountList.value?.map { item ->
            if (item.account == account) {
                item.copy(
                    loading = loading
                )
            } else {
                item
            }
        }
        _accountList.update { accountListUpdated }
    }
}
